<?php $__env->startSection('title', $profile->public_name . ' - Tutor Profesional Ngambiskuy'); ?>

<?php $__env->startSection('meta'); ?>
<meta name="description" content="<?php echo e($profile->description ?: 'Tutor profesional di Ngambiskuy dengan ' . $stats['total_courses'] . ' kursus dan ' . $stats['total_learners'] . ' siswa.'); ?>">
<meta name="keywords" content="tutor, <?php echo e($profile->public_name); ?>, kursus online, pembelajaran, <?php echo e($profile->user->skills ?? 'pendidikan'); ?>">
<meta property="og:title" content="<?php echo e($profile->public_name); ?> - Tutor Profesional Ngambiskuy">
<meta property="og:description" content="<?php echo e($profile->description); ?>">
<meta property="og:image" content="<?php echo e($profile->user->getProfilePictureUrl()); ?>">
<meta property="og:url" content="<?php echo e($profile->getPublicUrlAttribute()); ?>">
<meta name="twitter:card" content="summary_large_image">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-primary via-primary-dark to-secondary overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
        </svg>
    </div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <!-- Tutor Info -->
            <div class="lg:col-span-2">
                <div class="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8">
                    <!-- Profile Picture -->
                    <div class="relative">
                        <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-2xl">
                            <img src="<?php echo e($profile->user->getProfilePictureUrl()); ?>"
                                 alt="<?php echo e($profile->public_name); ?>"
                                 class="w-full h-full object-cover">
                        </div>
                        <!-- Verified Badge -->
                        <div class="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2 shadow-lg">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Tutor Details -->
                    <div class="flex-1 text-white">
                        <div class="flex flex-wrap items-center gap-3 mb-3">
                            <h1 class="text-4xl font-bold"><?php echo e($profile->public_name); ?></h1>
                            <?php if($profile->user->job_title): ?>
                                <span class="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <?php echo e($profile->user->job_title); ?>

                                </span>
                            <?php endif; ?>
                        </div>

                        <p class="text-xl text-white/90 mb-4"><?php echo e($profile->description); ?></p>

                        <!-- Quick Stats -->
                        <div class="flex flex-wrap gap-6 mb-6">
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <span class="font-semibold"><?php echo e(number_format($stats['average_rating'], 1)); ?></span>
                                <span class="text-white/80">Rating</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                                </svg>
                                <span class="font-semibold"><?php echo e(number_format($stats['total_learners'])); ?></span>
                                <span class="text-white/80">Siswa</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                                <span class="font-semibold"><?php echo e($stats['total_content']); ?></span>
                                <span class="text-white/80">Konten</span>
                            </div>
                        </div>

                        <!-- Location & Experience -->
                        <div class="flex flex-wrap items-center gap-4 text-white/80">
                            <?php if($profile->user->location): ?>
                                <div class="flex items-center space-x-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <span><?php echo e($profile->user->location); ?></span>
                                </div>
                            <?php endif; ?>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span><?php echo e($stats['years_experience']); ?>+ tahun pengalaman</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                                </svg>
                                <span><?php echo e($profile->education_level); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Card -->
            <div class="lg:col-span-1">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <h3 class="text-white font-semibold text-lg mb-4">Hubungi Tutor</h3>
                    
                    <!-- Contact Button -->
                    <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
                       target="_blank"
                       class="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors mb-4">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <span>Chat WhatsApp</span>
                    </a>

                    <!-- Member Since -->
                    <div class="text-center text-white/80 text-sm">
                        <p>Bergabung sejak <?php echo e($profile->created_at->format('M Y')); ?></p>
                    </div>

                    <!-- Social Links -->
                    <?php if($profile->user->hasSocialMediaLinks() || $profile->user->website): ?>
                        <div class="mt-4 pt-4 border-t border-white/20">
                            <div class="flex justify-center space-x-3">
                                <?php $__currentLoopData = $profile->user->getSocialMediaLinks(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e($url); ?>" target="_blank" rel="noopener noreferrer"
                                       class="text-white/70 hover:text-white transition-colors">
                                        <?php if($platform === 'linkedin'): ?>
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                            </svg>
                                        <?php elseif($platform === 'github'): ?>
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                            </svg>
                                        <?php endif; ?>
                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if($profile->user->website): ?>
                                    <a href="<?php echo e($profile->user->website); ?>" target="_blank" rel="noopener noreferrer"
                                       class="text-white/70 hover:text-white transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
                                        </svg>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Skills & Expertise -->
    <?php if($profile->user->getSkillsArray()): ?>
    <div class="bg-white rounded-xl shadow-lg p-8 mb-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Keahlian & Expertise</h2>
        <div class="flex flex-wrap gap-3">
            <?php $__currentLoopData = $profile->user->getSkillsArray(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <span class="bg-gradient-to-r from-primary to-primary-dark text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                    <?php echo e($skill); ?>

                </span>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Featured Courses -->
    <?php if($featuredCourses->count() > 0): ?>
    <div class="bg-white rounded-xl shadow-lg p-8 mb-12">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-900">Kursus Terpopuler</h2>
            <?php if($stats['total_courses'] > 3): ?>
                <a href="#all-courses" class="text-primary hover:text-primary-dark font-medium">
                    Lihat Semua →
                </a>
            <?php endif; ?>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <?php $__currentLoopData = $featuredCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="group bg-gray-50 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-200">
                    <div class="aspect-video bg-gray-200 relative overflow-hidden">
                        <?php if($course->thumbnail): ?>
                            <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                 alt="<?php echo e($course->title); ?>"
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        <?php else: ?>
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary to-primary-dark">
                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            </div>
                        <?php endif; ?>

                        <!-- Price Badge -->
                        <div class="absolute top-3 right-3">
                            <?php if($course->is_free): ?>
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">Gratis</span>
                            <?php else: ?>
                                <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                            <?php endif; ?>
                        </div>

                        <!-- Students Count -->
                        <div class="absolute bottom-3 left-3">
                            <span class="bg-black/70 text-white px-2 py-1 rounded text-xs font-medium">
                                <?php echo e($course->total_students); ?> siswa
                            </span>
                        </div>
                    </div>

                    <div class="p-6">
                        <?php if($course->category): ?>
                            <span class="inline-block bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs font-medium mb-2">
                                <?php echo e($course->category->name); ?>

                            </span>
                        <?php endif; ?>

                        <h3 class="font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                            <?php echo e($course->title); ?>

                        </h3>

                        <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($course->description); ?></p>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-1">
                                <div class="flex text-yellow-400">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <?php if($i <= floor($course->average_rating)): ?>
                                            <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                            </svg>
                                        <?php else: ?>
                                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                            </svg>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </div>
                                <span class="text-sm text-gray-600"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                            </div>

                            <a href="<?php echo e(route('course.show', $course)); ?>"
                               class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Lihat Detail
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Content Tabs -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden" id="all-courses">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-8 pt-6" aria-label="Tabs">
                <button onclick="showTab('courses')" id="courses-tab"
                        class="tab-button active whitespace-nowrap py-3 px-1 border-b-2 font-semibold text-lg">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                        <span>Kursus (<?php echo e($stats['total_courses']); ?>)</span>
                    </div>
                </button>
                <button onclick="showTab('exams')" id="exams-tab"
                        class="tab-button whitespace-nowrap py-3 px-1 border-b-2 font-semibold text-lg">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Ujian (<?php echo e($stats['total_exams']); ?>)</span>
                    </div>
                </button>
                <button onclick="showTab('blogs')" id="blogs-tab"
                        class="tab-button whitespace-nowrap py-3 px-1 border-b-2 font-semibold text-lg">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                        <span>Blog (<?php echo e($stats['total_blog_posts']); ?>)</span>
                    </div>
                </button>
            </nav>
        </div>

        <!-- Courses Tab -->
        <div id="courses-content" class="tab-content p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Semua Kursus dari <?php echo e($profile->public_name); ?></h2>

            <?php if($courses->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                            <div class="aspect-video bg-gray-100 relative">
                                <?php if($course->thumbnail): ?>
                                    <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                         alt="<?php echo e($course->title); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300">
                                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                        </svg>
                                    </div>
                                <?php endif; ?>

                                <!-- Price Badge -->
                                <div class="absolute top-3 right-3">
                                    <?php if($course->is_free): ?>
                                        <span class="bg-green-500 text-white px-2 py-1 rounded text-sm font-medium">Gratis</span>
                                    <?php else: ?>
                                        <span class="bg-blue-500 text-white px-2 py-1 rounded text-sm font-medium">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="p-4">
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                                    <?php if($course->category): ?>
                                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs"><?php echo e($course->category->name); ?></span>
                                    <?php endif; ?>
                                    <span><?php echo e($course->level); ?></span>
                                </div>

                                <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary transition-colors"><?php echo e($course->title); ?></h3>
                                <p class="text-gray-600 text-sm mb-3 line-clamp-2"><?php echo e($course->description); ?></p>

                                <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                    <span><?php echo e($course->total_lessons); ?> pelajaran</span>
                                    <span><?php echo e($course->total_duration_minutes); ?> menit</span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <?php if($i <= floor($course->average_rating)): ?>
                                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                    </svg>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="ml-1 text-sm"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                    </div>

                                    <a href="<?php echo e(route('course.show', $course)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                        Lihat Detail →
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($stats['total_courses'] > 6): ?>
                    <div class="text-center mt-8">
                        <a href="<?php echo e(route('courses.index')); ?>?tutor=<?php echo e($profile->user->id); ?>"
                           class="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                            Lihat Semua Kursus (<?php echo e($stats['total_courses']); ?>)
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Kursus</h3>
                    <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat kursus. Pantau terus untuk update terbaru!</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Exams Tab -->
        <div id="exams-content" class="tab-content p-8 hidden">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Ujian dari <?php echo e($profile->public_name); ?></h2>

            <?php if($exams->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <?php if($exam->category): ?>
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium"><?php echo e($exam->category->name); ?></span>
                                    <?php endif; ?>
                                    <span class="bg-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-100 text-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-800 px-2 py-1 rounded text-xs font-medium">
                                        <?php echo e(ucfirst($exam->difficulty_level)); ?>

                                    </span>
                                </div>

                                <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary transition-colors"><?php echo e($exam->title); ?></h3>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($exam->description); ?></p>

                                <div class="space-y-2 mb-4">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <?php echo e($exam->time_limit); ?> menit
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                        <?php echo e($exam->total_questions); ?> soal
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Passing score: <?php echo e($exam->passing_score); ?>%
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="text-lg font-bold text-primary">
                                        <?php if($exam->price > 0): ?>
                                            Rp <?php echo e(number_format($exam->price, 0, ',', '.')); ?>

                                        <?php else: ?>
                                            Gratis
                                        <?php endif; ?>
                                    </div>

                                    <a href="<?php echo e(route('exams.show', $exam)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                        Lihat Detail →
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($stats['total_exams'] > 6): ?>
                    <div class="text-center mt-8">
                        <a href="<?php echo e(route('exams.index')); ?>?tutor=<?php echo e($profile->user->id); ?>"
                           class="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                            Lihat Semua Ujian (<?php echo e($stats['total_exams']); ?>)
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Ujian</h3>
                    <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat ujian. Pantau terus untuk update terbaru!</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Blogs Tab -->
        <div id="blogs-content" class="tab-content p-8 hidden">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Blog dari <?php echo e($profile->public_name); ?></h2>

            <?php if($blogPosts->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php $__currentLoopData = $blogPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <article class="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                            <?php if($post->featured_image): ?>
                                <div class="aspect-video bg-gray-100">
                                    <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>"
                                         alt="<?php echo e($post->title); ?>"
                                         class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                </div>
                            <?php endif; ?>

                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 mb-3">
                                    <?php if($post->category): ?>
                                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs mr-3"><?php echo e($post->category->name); ?></span>
                                    <?php endif; ?>
                                    <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>">
                                        <?php echo e($post->published_at->format('d M Y')); ?>

                                    </time>
                                    <span class="mx-2">•</span>
                                    <span><?php echo e($post->read_time); ?> menit baca</span>
                                </div>

                                <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                                    <a href="<?php echo e(route('blog.show', $post)); ?>">
                                        <?php echo e($post->title); ?>

                                    </a>
                                </h3>

                                <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                        <?php echo e($post->views_count); ?> views
                                    </div>

                                    <a href="<?php echo e(route('blog.show', $post)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                        Baca Selengkapnya →
                                    </a>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($stats['total_blog_posts'] > 6): ?>
                    <div class="text-center mt-8">
                        <a href="<?php echo e(route('blog.index')); ?>?author=<?php echo e($profile->user->id); ?>"
                           class="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                            Lihat Semua Blog (<?php echo e($stats['total_blog_posts']); ?>)
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Blog</h3>
                    <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum menulis blog. Pantau terus untuk update terbaru!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- About Section -->
    <?php if($profile->description || $profile->long_description): ?>
    <div class="bg-white rounded-xl shadow-lg p-8 mt-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang <?php echo e($profile->public_name); ?></h2>

        <?php if($profile->long_description): ?>
            <!-- Long Description (Main About Section) -->
            <div class="prose prose-gray max-w-none mb-6">
                <div class="text-gray-700 leading-relaxed whitespace-pre-line"><?php echo e($profile->long_description); ?></div>
            </div>
        <?php elseif($profile->description): ?>
            <!-- Fallback to Short Description if no long description -->
            <div class="prose prose-gray max-w-none mb-6">
                <p class="text-gray-700 leading-relaxed"><?php echo e($profile->description); ?></p>
            </div>
        <?php endif; ?>

        <!-- Education & Professional Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8 pt-8 border-t border-gray-200">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Latar Belakang Pendidikan</h3>
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900"><?php echo e($profile->education_level); ?></p>
                        <p class="text-sm text-gray-500">Tingkat Pendidikan</p>
                    </div>
                </div>
            </div>

            <?php if($profile->user->company): ?>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengalaman Profesional</h3>
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900"><?php echo e($profile->user->company); ?></p>
                        <?php if($profile->user->job_title): ?>
                            <p class="text-sm text-gray-500"><?php echo e($profile->user->job_title); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Back to Home -->
    <div class="text-center mt-12">
        <a href="<?php echo e(route('home')); ?>"
           class="inline-flex items-center space-x-2 text-primary hover:text-primary-dark font-medium transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            <span>Kembali ke Beranda</span>
        </a>
    </div>
</div>

<style>
.tab-button {
    @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors;
}

.tab-button.active {
    @apply border-primary text-primary;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    const selectedContent = document.getElementById(tabName + '-content');
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    }

    // Add active class to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
}

// Initialize the first tab as active on page load
document.addEventListener('DOMContentLoaded', function() {
    showTab('courses');
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/tutor/public-profile-v2.blade.php ENDPATH**/ ?>